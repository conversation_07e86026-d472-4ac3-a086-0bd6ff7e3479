"use client";

import AppButton from "@/components/common/AppButton";
import RatingStars from "@/components/common/RatingStars";
import COLORS from "@/constants/colors";
import TempData from "@/data";
import { cn, parseTimeToDate } from "@/lib/utils";
import ICONS from "@/public/icons";
import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";
import React, { useState } from "react";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/common/Accordion";
import { ClubsQuery } from "@/generated/graphql";
import { useHandleToggleAccordion } from "@/hooks/use-utils";

type ClubDetailSectionProps = {
  club: ClubsQuery["clubs"]["docs"][0];
};

const today = new Date();
const dayOfWeek = format(today, "EEEE").toUpperCase();

export default function ClubDetailSection({ club }: ClubDetailSectionProps) {
  const clubData = club;
  const scheduleLength = clubData.businessHours?.schedule?.length || 0;

  const [selectedImage, setSelectedImage] = useState<number>(0);
  const { activeIndex, handleToggle } = useHandleToggleAccordion(0);

  return (
    <section>
      <div className="w-full lg:pt-[138px] lg:pb-[105px] bg-transparent lg:bg-gradient-to-br lg:from-text lg:from-55% lg:to-secondary">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between px-4 py-5 container md:gap-[20px] xl:gap-[58px]">
          <div className="flex flex-col gap-3 xl:gap-3.5 mb-[33px] w-full">
            <Image
              src={clubData.images[selectedImage] || "/svg/cheers.svg"}
              width={365}
              height={294}
              alt="club1"
              className="rounded-[18px] w-full h-[294px] lg:h-[501px] object-cover transition-all duration-300 ease-in-out aspect-square"
              quality={100}
              loading="eager"
              priority={true}
            />
            <div className="grid grid-cols-4 gap-2 xl:gap-3.5 items-center w-full">
              {clubData.images.map((image, idx) => (
                <button
                  key={idx}
                  onClick={() => setSelectedImage(idx)}
                  className={cn(
                    "bg-transparent cursor-pointer overflow-hidden rounded-[18px]",
                    idx === selectedImage &&
                      "ring-[3px] lg:ring-[6px] ring-primary"
                  )}
                >
                  <Image
                    src={image || "/svg/tempLogo.svg"}
                    width={85}
                    height={92}
                    alt={`club-image-${idx}`}
                    className="w-full h-[92px] lg:h-[156px] object-cover aspect-square"
                    quality={100}
                    loading="eager"
                    priority={true}
                  />
                </button>
              ))}
            </div>
          </div>
          <div className="w-full">
            <RatingStars
              className="bg-transparent border-0 mb-7 lg:hidden"
              ratingValue={clubData?.rating || 0}
              width={23}
              height={22}
              textStyles="text-footerbg text-[24px] font-bold ml-2"
            />
            <RatingStars
              className="bg-transparent border-0 lg:mb-5 hidden lg:block"
              ratingValue={clubData?.rating || 0}
              width={35}
              height={32}
              textStyles="text-footerbg text-[24px] font-bold ml-2"
            />
            <div className="text-[32px] font-bold leading-9 text-footerbg xl:text-6xl">
              {clubData?.name}
            </div>
            <div className="flex flex-row items-center w-full mt-5 gap-2 mb-5.5 xl:mt-[50px]">
              <div className="min-w-[53px] min-h-[53px] rounded-full bg-footerbg flex items-center justify-center">
                <ICONS.PinIcon
                  width={22}
                  height={29}
                  color={COLORS.LIGHT.TEXT}
                  className="lg:hidden"
                />
                <ICONS.PinIcon
                  width={28}
                  height={32}
                  color={COLORS.LIGHT.TEXT}
                  className="hidden lg:flex"
                />
              </div>
              <div className="text-sm font-medium text-footerbg lg:text-xl w-full">
                {clubData?.address?.address}
              </div>
            </div>
            <div className="w-full flex flex-row items-center gap-1.5 flex-wrap mb-[28px]">
              {clubData?.categories?.slice(0, 2).map((tag, idx) => (
                <button
                  key={idx}
                  className="text-text text-xl lg:text-2xl font-medium bg-muted-primary rounded-[4px] px-2 py-1 flex items-center justify-center"
                >
                  {tag?.name}
                </button>
              ))}
              {clubData?.categories?.length > 2 && (
                <button className="text-text text-xl lg:text-2xl font-medium bg-transparent ring-2 ring-muted-primary rounded-[4px] px-2 py-0.5 flex items-center justify-center">
                  +{clubData.categories.length - 2}
                </button>
              )}
            </div>

            <div className="flex flex-row items-center gap-2 text-xl lg:text-2xl text-muted-primary font-bold mb-[25px] lg:mb-[30px]">
              <div>Contact:</div>
              <Link
                className="hover:underline hover:underline-offset-2 text-muted-primary"
                href={`tel:${clubData?.contact?.countryCode} ${clubData?.contact?.phone}`}
              >
                {clubData?.contact?.countryCode} {clubData?.contact?.phone}
              </Link>
            </div>
            <AppButton
              variant="gradient4"
              className="px-10 sm:px-[28px] py-[13px] mb-[33px] lg:text-xl font-bold font-montserrat leading-5 max-w-[215px]"
            >
              BOOK A TABLE
            </AppButton>
          </div>
        </div>
      </div>

      {/* CLUB DESCRIPTION */}
      <div className="w-full text-sm md:text-xl leading-[29px] text-text md:mt-[60px] md:mb-[103px] mb-[37px] container">
        {clubData?.description}
      </div>

      {/* DETAILS */}
      <div className="w-full grid grid-cols-1 lg:grid-cols-2 md:gap-16 container px-4 mb-[60px] lg:mb-[80px]">
        {/* OPENING HOURS */}
        <div className="w-full flex flex-col gap-4">
          <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
            Opening hours:
          </h2>
          <div className="bg-transparent sm:bg-backgroundlight py-1 sm:py-[28px] sm:px-[25px] sm:mt-[27px] mb-8 sm:mb-0">
            {clubData?.businessHours?.schedule?.map((schedule, idx) => (
              <div
                key={idx}
                className={cn(
                  "flex flex-row items-center justify-between py-[7px] sm:py-[12px] border-b border-secondary",
                  idx === scheduleLength - 1 && "border-b-0"
                )}
              >
                <div
                  className={cn(
                    "text-sm sm:text-xl leading-5 text-secondary",
                    schedule.day === dayOfWeek && "font-extrabold"
                  )}
                >
                  {schedule.day}
                </div>
                <div
                  className={cn(
                    "text-sm sm:text-xl leading-5 font-bold text-secondary",
                    schedule.day === dayOfWeek && "font-extrabold"
                  )}
                >
                  {format(parseTimeToDate(schedule.timings[0]), "h:mm a")} -{" "}
                  {format(parseTimeToDate(schedule.timings[1]), "h:mm a")}
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* MENU */}
        <div
          className={cn(
            "w-full h-[550px] max-h-[580px] flex flex-col gap-4",
            activeIndex === null && "h-full max-h-full"
          )}
        >
          <h2 className="font-bold text-primary text-xl sm:text-2xl leading-5">
            Menu
          </h2>
          <div className="w-full h-full overflow-y-auto hide-scrollbar flex flex-col gap-4">
            {clubData?.menu?.sections.map((menu, idx) => (
              <Accordion
                key={idx}
                className="w-full flex flex-col gap-2 lg:gap-3"
              >
                <div>
                  <AccordionTrigger
                    className="justify-between bg-footerbg rounded-[8px] py-2 text-secondary text-xl lg:text-xl"
                    onClick={handleToggle(idx)}
                    isActive={activeIndex === idx}
                  >
                    {menu.name}
                    {activeIndex === idx ? (
                      <ICONS.ChevronUpIcon
                        width={13}
                        height={7}
                        color={COLORS.LIGHT.SECONDARY}
                      />
                    ) : (
                      <ICONS.ChevronDownIcon
                        width={13}
                        height={7}
                        color={COLORS.LIGHT.SECONDARY}
                      />
                    )}
                  </AccordionTrigger>

                  <div
                    className={cn(
                      "max-h-[230px] overflow-y-auto scrollbar-theme relative",
                      activeIndex === idx && "pt-8"
                    )}
                  >
                    {activeIndex === idx && (
                      <div className="flex flex-row items-center justify-between w-full py-1.5 lg:py-2 px-2 bg-text absolute -top-1 rounded-b-[8px]">
                        <div className="w-full pl-2">
                          <ICONS.BeerJugIcon
                            width={20}
                            height={21}
                            color={COLORS.LIGHT.MUTED_PRIMARY}
                          />
                        </div>
                        <div className="text-xs sm:text-base xl:text-xl font-medium text-muted-primary truncate  absolute right-3">
                          Standard
                        </div>
                      </div>
                    )}
                    <AccordionItem isActive={activeIndex === idx}>
                      {menu.items.map((item, itemIdx) => (
                        <div
                          key={itemIdx}
                          className={cn(
                            "flex flex-row justify-between p-1.5 lg:px-4 mx-1",
                            menu.items.length - 1 === itemIdx
                              ? "border-b-0"
                              : "border-b border-secondary/30"
                          )}
                        >
                          <div className="text-base xl:text-xl font-bold text-secondary max-w-[50%] sm:max-w-[180px]">
                            {item.name}
                          </div>
                          <div className="text-base lg:text-xl font-medium text-secondary">
                            {clubData?.menu?.currency} {item.price}
                          </div>
                        </div>
                      ))}
                    </AccordionItem>
                  </div>
                </div>
              </Accordion>
            ))}
          </div>
        </div>
      </div>

      {/* SERVICES OFFERED (DISABLED) */}
      {/* <div className="flex flex-col px-4 container mb-[60px] lg:mb-[80px]">
        <h2 className="font-bold text-primary text-xl leading-5 mb-9 lg:mb-11">
          Services Offered
        </h2>
        <div className="flex flex-wrap gap-[13px] lg:gap-7">
          {menuData[0].services.map((service, idx) => (
            <div
              key={idx}
              className="inline-flex items-center gap-[13px] text-secondary text-lg lg:text-2xl font-bold bg-footerbg p-[11px] rounded-[8px]"
            >
              {service.available ? (
                <ICONS.BoxTickIcon
                  width={30}
                  height={30}
                  color={COLORS.LIGHT.SUCCESS}
                />
              ) : (
                <ICONS.BoxCrossIcon
                  width={30}
                  height={30}
                  color={COLORS.LIGHT.FAILED}
                />
              )}
              {service.name}
            </div>
          ))}
        </div>
      </div> */}
    </section>
  );
}
