import AppButton from "@/components/common/AppButton";
import { CitiesQuery } from "@/generated/graphql";
import Image from "next/image";
import Link from "next/link";
import React from "react";

type HeroSectionProps = CitiesQuery["cities"]["docs"][0];

export default function HeroSection({
  heading,
  subHeading,
  coverImage,
}: HeroSectionProps) {
  return (
    <div className="w-full h-[593px] md:h-[774px] bg-red-100 relative flex flex-1 items-center justify-center">
      <Image
        alt="hero"
        src={coverImage || "/svg/tempLogo.svg"}
        priority
        fill
        className="object-cover aspect-square md:object-cover transition-all duration-500"
        loading="eager"
        quality={100}
        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
      />

      {/* GRADIENT */}
      <div className="w-full h-full absolute city-banner-gradient opacity-45 bg-blend-multiply" />

      {/* CONTENT */}
      <div className="absolute bottom-0 lg:relative mb-12 px-6 container">
        <p className="text-4xl md:text-5xl  xl:text-[64px] font-montserrat-alternates font-bold max-w-[262px] md:max-w-[55%] lg:max-w-[350px] xl:max-w-[463px] leading-11 text-white md:leading-[60px]  xl:leading-[79px] mb-1.5 md:mb-[19px]">
          {heading}
        </p>
        <p className="text-base md:text-xl lg:text-2xl leading-[21px] md:leading-[31px] font-montserrat-alternates font-semibold text-muted-primary max-w-[90%] md:max-w-[50%]  xl:max-w-[398px] ">
          {subHeading}
        </p>
        <Link href="/#explore">
          <AppButton variant="outline" className="my-4 md:my-8 py-3 px-8">
            EXPLORE NOW
          </AppButton>
        </Link>
      </div>
    </div>
  );
}
