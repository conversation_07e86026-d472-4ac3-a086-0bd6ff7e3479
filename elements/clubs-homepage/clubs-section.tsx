import { fetchData } from "@/client";
import GridCard from "@/components/cards/grid.card";
import PopularCard from "@/components/cards/popular.card";
import AppButton from "@/components/common/AppButton";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import TempData from "@/data";
import {
  CitiesQuery,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import Link from "next/link";
import { useCallback } from "react";

type ClubsProps = {
  cityId: string;
};

export default async function ClubsSection({ cityId }: ClubsProps) {
  const { clubs: popularClubs } = await fetchData<
    ClubsQuery,
    ClubsQueryVariables
  >(ClubsDocument, {
    clubsInput: { city: cityId, featured: true },
    paginationInput: { limit: 16 },
  })();

  const {
    clubCategories: { docs: clubCategories },
  } = await fetchData<ClubCategoriesQuery, ClubCategoriesQueryVariables>(
    ClubCategoriesDocument
  )();

  return (
    <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
      <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
        <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
          Popular Clubs
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
          {popularClubs?.docs?.map((club) => (
            <Link href={`clubs/${club.slug}`} key={club.id}>
              <GridCard
                name={club.name}
                rating={club.rating}
                avgCost=""
                image={club.coverImage!}
              />
            </Link>
          ))}
        </div>
      </section>

      {/* Show bars with categories */}
      {clubCategories.map(async (c) => {
        const { clubs: clubsWithFilter } = await fetchData<
          ClubsQuery,
          ClubsQueryVariables
        >(ClubsDocument, {
          clubsInput: {
            city: cityId,
            categories: [c.id],
          },
          paginationInput: { limit: 16 },
        })();

        return (
          <section
            className="w-full flex flex-col items-center container mb-[45px]"
            key={c.id}
          >
            <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
              {c.name}
            </h2>
            <div className="w-full flex flex-col items-center justify-center">
              <HorizontalScrollCards>
                {clubsWithFilter?.docs?.map((card) => (
                  <Link href={`clubs/${card.slug}`} key={card.id}>
                    <PopularCard data={card} cardType="club" />
                  </Link>
                ))}
              </HorizontalScrollCards>
              <Link href={{ query: { category: c.name } }}>
                <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-[53px]">
                  View all
                </AppButton>
              </Link>
            </div>
          </section>
        );
      })}
    </div>
  );
}
