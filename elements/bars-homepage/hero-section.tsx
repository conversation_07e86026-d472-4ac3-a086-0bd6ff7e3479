import React from "react";

export default function HeroSection({
  category,
  description,
}: {
  category: string | undefined;
  description: string | undefined | null;
}) {
  return (
    <section className="w-full py-[112px] md:py-[128px] bars-banner-gradient flex flex-col items-center justify-center">
      <div className="container w-full flex flex-col items-center justify-center">
        <div className="font-bold text-text text-[32px] leading-10.5 text-center sm:text-[40px] sm:leading-[52px]">
          {category}
        </div>
        <p className="text-sm text-text sm:text-xl mt-[40px] sm:mt-[57px] text-center">
          {description}
        </p>
      </div>
    </section>
  );
}
