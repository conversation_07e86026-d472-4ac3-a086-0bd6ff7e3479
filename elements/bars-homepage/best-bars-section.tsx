import {
  BarCategoriesQuery,
  BarsQuery,
  CitiesQuery,
} from "@/generated/graphql";
import Image from "next/image";
import React from "react";

type BarProps = {
  bars: BarsQuery["bars"];
  city: CitiesQuery["cities"]["docs"][number];
  categories: BarCategoriesQuery["barCategories"]["docs"];
};
export default function BestBarsSection({ bars, city, categories }: BarProps) {
  const barsdata = bars;
  const cityName = city.name;
  const totalBarCatogories = categories.length;
  const featuredBars = barsdata.docs.filter((bar) => bar.featured).length;
  return (
    <section className="flex flex-col bg-[#FFFBF3] sm:flex-row sm:items-center sm:bg-background container sm:gap-[28px] xl:gap-[56px]">
      <Image
        src={"/temp/bestbars-temp.jpg"}
        width={362}
        height={273}
        className="w-full h-auto sm:min-w-[280px] sm:h-auto lg:min-w-[350px] lg:h-auto xl:min-w-[604px] xl:h-auto rounded-[18px] mt-[42px] mb-6 xl:mt-[97px] xl:mb-[117px] object-cover"
        alt="best-bars"
        loading="eager"
        quality={100}
      />
      <div className="flex flex-col">
        <h2 className="text-2xl lg:text-3xl xl:text-[60px] leading-[37px] xl:leading-[67.2px] font-bold mt-6 sm:mt-0 text-text">
          The Best Bars In {cityName}
        </h2>
        <p className="text-sm lg:text-base xl:text-xl text-text mt-6 sm:mb-4 mb-11.5 xl:mb-12 lg:max-w-[90%] lg:leading-[32px]">
          Lorem Ipsum has been the industry's standard Lorem Ipsum is simply
          dummy text of the printing and typesetting industry.Lorem Ipsum is
          simply dummy text of the printing and typesetting industry.
        </p>
        <div className="flex flex-row flex-wrap gap-[43px] sm:gap-0 sm:w-full">
          <div className="flex flex-1 flex-col sm:items-center sm:justify-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px] sm:border-r">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Total Bars
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {barsdata.totalDocs}+
            </div>
          </div>
          <div className="flex flex-1 flex-col sm:items-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px] sm:border-r">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Total Categories
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {totalBarCatogories}+
            </div>
          </div>
          <div className="flex flex-1 flex-col sm:items-center gap-[19px] sm:gap-2 xl:gap-[19px] sm:border-t sm:border-inactive sm:pt-[20px] xl:pt-[39px]">
            <p className="text-text font-medium leading-[23px] text-base sm:text-sm xl:text-base">
              Popular Bars
            </p>
            <div className="text-muted-primary font-bold text-4xl sm:text-[22px] xl:text-[50px] leading-14">
              {featuredBars}+
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
