import { fetchData } from "@/client";
import GridCard from "@/components/cards/grid.card";
import PopularCard from "@/components/cards/popular.card";
import AppButton from "@/components/common/AppButton";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
} from "@/generated/graphql";
import Link from "next/link";

type BarsProps = {
  cityId: string;
};

const SkeletonGridCard = () => (
  <div className="w-full h-full flex flex-row items-center rounded-[18px] bg-white border border-inactive/30 shadow-xl shadow-inactive/50">
    {/* Image Placeholder */}
    <div className="xl:w-[355px] sm:h-[180px] xl:h-[180px] sm:w-[280px] w-[280px] h-[180px] lg:w-[290px] lg:h-[180px] shimmer rounded-xl" />
    <div className="w-full h-full flex flex-col justify-center pl-4 gap-1">
      {/* Title Placeholder */}
      <div className="w-1/2 h-3 shimmer rounded" />
      <div className="w-3/4 h-4 shimmer rounded mt-3" />
      <div className="flex flex-row items-center gap-1">
        <div className="w-1/2 h-3 shimmer rounded mt-3" />
      </div>
    </div>
  </div>
);

export default async function BarsSection({ cityId }: BarsProps) {
  const { bars: popularBars } = await fetchData<BarsQuery, BarsQueryVariables>(
    BarsDocument,
    {
      barsInput: { city: cityId, featured: true },
      paginationInput: { limit: 16 },
    }
  )();

  const {
    barCategories: { docs: barCategories },
  } = await fetchData<BarCategoriesQuery, BarCategoriesQueryVariables>(
    BarCategoriesDocument
  )();

  return (
    <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
      <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
        <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
          Popular Bars
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
          {popularBars?.docs.map((bar) => (
            <Link href={`bars/${bar.slug}`} key={bar.id}>
              <GridCard
                name={bar.name}
                rating={bar.rating}
                avgCost=""
                image={bar.coverImage!}
              />
            </Link>
          ))}
        </div>
      </section>

      {/* Show bars with categories */}
      {barCategories.map(async (c) => {
        const { bars: barsWithFilter } = await fetchData<
          BarsQuery,
          BarsQueryVariables
        >(BarsDocument, {
          barsInput: {
            city: cityId,
            categories: [c.id],
          },
          paginationInput: { limit: 16 },
        })();

        return (
          <section
            className="w-full flex flex-col items-center container mb-[45px]"
            key={c.id}
          >
            <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
              {c.name}
            </h2>
            <div className="w-full flex flex-col items-center justify-center">
              <HorizontalScrollCards>
                {barsWithFilter?.docs?.map((card) => (
                  <Link href={`bars/${card.slug}`} key={card.id}>
                    <PopularCard data={card} cardType="bar" />
                  </Link>
                ))}
              </HorizontalScrollCards>
              <Link href={{ query: { category: c.name } }}>
                <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-[53px]">
                  View all
                </AppButton>
              </Link>
            </div>
          </section>
        );
      })}
    </div>
  );
}
