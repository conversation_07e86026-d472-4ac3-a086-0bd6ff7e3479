import { IconProps } from "@/types";
import * as React from "react";
const YoutubeIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 16 12"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.56507 3.87741L10.1017 5.99936L6.56507 8.12131V3.87741ZM12.7541 1.83326C13.4756 1.8757 13.61 1.93936 13.8363 2.35667C14.1687 2.9296 14.3456 4.06131 14.3456 6.00643C14.3456 7.95155 14.1687 9.06911 13.8434 9.63497C13.617 10.0523 13.4826 10.1159 12.7541 10.1655C11.976 10.2079 9.9319 10.2433 7.97971 10.2433C6.02751 10.2433 3.98337 10.2079 3.19117 10.1655C2.47678 10.1159 2.34239 10.0523 2.11605 9.64911C1.78361 9.06911 1.60678 7.93033 1.60678 5.99936C1.60678 4.06838 1.78361 2.93667 2.10897 2.35667C2.33532 1.94643 2.46971 1.88277 3.1841 1.84033C3.94093 1.79082 5.87897 1.76253 7.97263 1.76253C10.0663 1.76253 11.9973 1.79082 12.747 1.84033L12.7541 1.83326ZM15.067 10.3352C15.548 9.50765 15.7602 8.17082 15.7602 5.99936C15.7602 3.82789 15.5551 2.50521 15.067 1.65643C14.579 0.772284 13.999 0.49643 12.846 0.425698C12.0609 0.369113 10.1017 0.34082 7.97971 0.34082C5.85776 0.34082 3.89849 0.369113 3.10629 0.425698C1.96044 0.489357 1.38044 0.772284 0.885316 1.65643C0.404341 2.50521 0.199219 3.83497 0.199219 6.00643C0.199219 8.17789 0.404341 9.50058 0.89239 10.3423C1.38044 11.2194 1.95337 11.4952 3.10629 11.573C3.93385 11.6225 6.00629 11.6508 7.97971 11.6508C9.95312 11.6508 12.0185 11.6155 12.846 11.573C14.0131 11.4881 14.586 11.2123 15.0741 10.3281L15.067 10.3352Z"
      fill={color}
    />
  </svg>
);
export default YoutubeIcon;
