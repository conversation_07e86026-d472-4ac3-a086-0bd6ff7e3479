import { IconProps } from "@/types";
import * as React from "react";
const EuroIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 52 13"
    fill="none"
    className={className}
  >
    <path
      d="M0.44875 8.064V6.944H9.05675V8.064H0.44875ZM0.44875 5.856V4.736H9.05675V5.856H0.44875ZM8.03275 12.16C7.16875 12.16 6.36875 12.0213 5.63275 11.744C4.90742 11.456 4.27808 11.056 3.74475 10.544C3.21142 10.0213 2.79542 9.408 2.49675 8.704C2.19808 8 2.04875 7.232 2.04875 6.4C2.04875 5.568 2.19808 4.8 2.49675 4.096C2.79542 3.392 3.21142 2.784 3.74475 2.272C4.27808 1.74933 4.90742 1.34933 5.63275 1.072C6.36875 0.783999 7.16875 0.639999 8.03275 0.639999C8.95008 0.639999 9.78742 0.8 10.5448 1.12C11.3128 1.42933 11.9528 1.89333 12.4648 2.512L11.1368 3.776C10.7314 3.33867 10.2781 3.01333 9.77675 2.8C9.27542 2.576 8.73142 2.464 8.14475 2.464C7.55808 2.464 7.01942 2.56 6.52875 2.752C6.04875 2.944 5.62742 3.216 5.26475 3.568C4.91275 3.92 4.63542 4.336 4.43275 4.816C4.23008 5.296 4.12875 5.824 4.12875 6.4C4.12875 6.976 4.23008 7.504 4.43275 7.984C4.63542 8.464 4.91275 8.88 5.26475 9.232C5.62742 9.584 6.04875 9.856 6.52875 10.048C7.01942 10.24 7.55808 10.336 8.14475 10.336C8.73142 10.336 9.27542 10.2293 9.77675 10.016C10.2781 9.792 10.7314 9.456 11.1368 9.008L12.4648 10.288C11.9528 10.896 11.3128 11.36 10.5448 11.68C9.78742 12 8.95008 12.16 8.03275 12.16ZM21.8223 12.16C21.0329 12.16 20.2596 12.048 19.5023 11.824C18.7556 11.5893 18.1263 11.2693 17.6143 10.864L18.4943 9.248C18.8996 9.57867 19.3903 9.84533 19.9663 10.048C20.5529 10.2507 21.1609 10.352 21.7903 10.352C22.5263 10.352 23.1023 10.1973 23.5183 9.888C23.9449 9.57867 24.1583 9.16267 24.1583 8.64C24.1583 8.288 24.0676 7.97867 23.8863 7.712C23.7156 7.44533 23.4063 7.24267 22.9583 7.104C22.5209 6.96533 21.9183 6.896 21.1503 6.896H18.4463L19.0223 0.799999H25.5983V2.544H19.7903L20.8783 1.552L20.4463 6.112L19.3583 5.136H21.6143C22.7236 5.136 23.6143 5.28533 24.2863 5.584C24.9689 5.872 25.4649 6.27733 25.7743 6.8C26.0943 7.312 26.2543 7.89867 26.2543 8.56C26.2543 9.21067 26.0943 9.808 25.7743 10.352C25.4543 10.896 24.9636 11.3333 24.3023 11.664C23.6516 11.9947 22.8249 12.16 21.8223 12.16ZM28.702 12.112C28.3393 12.112 28.03 11.9893 27.774 11.744C27.518 11.488 27.39 11.168 27.39 10.784C27.39 10.3787 27.518 10.0587 27.774 9.824C28.03 9.57867 28.3393 9.456 28.702 9.456C29.0647 9.456 29.374 9.57867 29.63 9.824C29.886 10.0587 30.014 10.3787 30.014 10.784C30.014 11.168 29.886 11.488 29.63 11.744C29.374 11.9893 29.0647 12.112 28.702 12.112ZM35.8448 12.16C34.9594 12.16 34.1594 11.936 33.4448 11.488C32.7408 11.04 32.1861 10.3893 31.7808 9.536C31.3754 8.672 31.1728 7.62667 31.1728 6.4C31.1728 5.17333 31.3754 4.13333 31.7808 3.28C32.1861 2.416 32.7408 1.76 33.4448 1.312C34.1594 0.863999 34.9594 0.639999 35.8448 0.639999C36.7408 0.639999 37.5408 0.863999 38.2448 1.312C38.9488 1.76 39.5034 2.416 39.9088 3.28C40.3248 4.13333 40.5328 5.17333 40.5328 6.4C40.5328 7.62667 40.3248 8.672 39.9088 9.536C39.5034 10.3893 38.9488 11.04 38.2448 11.488C37.5408 11.936 36.7408 12.16 35.8448 12.16ZM35.8448 10.352C36.3674 10.352 36.8208 10.2133 37.2048 9.936C37.5888 9.648 37.8874 9.21067 38.1008 8.624C38.3248 8.03733 38.4368 7.296 38.4368 6.4C38.4368 5.49333 38.3248 4.752 38.1008 4.176C37.8874 3.58933 37.5888 3.15733 37.2048 2.88C36.8208 2.592 36.3674 2.448 35.8448 2.448C35.3434 2.448 34.8954 2.592 34.5008 2.88C34.1168 3.15733 33.8128 3.58933 33.5888 4.176C33.3754 4.752 33.2688 5.49333 33.2688 6.4C33.2688 7.296 33.3754 8.03733 33.5888 8.624C33.8128 9.21067 34.1168 9.648 34.5008 9.936C34.8954 10.2133 35.3434 10.352 35.8448 10.352ZM46.6104 12.16C45.725 12.16 44.925 11.936 44.2104 11.488C43.5064 11.04 42.9517 10.3893 42.5464 9.536C42.141 8.672 41.9384 7.62667 41.9384 6.4C41.9384 5.17333 42.141 4.13333 42.5464 3.28C42.9517 2.416 43.5064 1.76 44.2104 1.312C44.925 0.863999 45.725 0.639999 46.6104 0.639999C47.5064 0.639999 48.3064 0.863999 49.0104 1.312C49.7144 1.76 50.269 2.416 50.6744 3.28C51.0904 4.13333 51.2984 5.17333 51.2984 6.4C51.2984 7.62667 51.0904 8.672 50.6744 9.536C50.269 10.3893 49.7144 11.04 49.0104 11.488C48.3064 11.936 47.5064 12.16 46.6104 12.16ZM46.6104 10.352C47.133 10.352 47.5864 10.2133 47.9704 9.936C48.3544 9.648 48.653 9.21067 48.8664 8.624C49.0904 8.03733 49.2024 7.296 49.2024 6.4C49.2024 5.49333 49.0904 4.752 48.8664 4.176C48.653 3.58933 48.3544 3.15733 47.9704 2.88C47.5864 2.592 47.133 2.448 46.6104 2.448C46.109 2.448 45.661 2.592 45.2664 2.88C44.8824 3.15733 44.5784 3.58933 44.3544 4.176C44.141 4.752 44.0344 5.49333 44.0344 6.4C44.0344 7.296 44.141 8.03733 44.3544 8.624C44.5784 9.21067 44.8824 9.648 45.2664 9.936C45.661 10.2133 46.109 10.352 46.6104 10.352Z"
      fill={color}
    />
  </svg>
);
export default EuroIcon;
