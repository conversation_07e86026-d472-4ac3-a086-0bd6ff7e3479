import { IconProps } from "@/types";
import * as React from "react";
const NavbarIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 25 22"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 2C0 1.17157 0.66076 0.5 1.47585 0.5H23.5241C24.3392 0.5 25 1.17157 25 2C25 2.82843 24.3392 3.5 23.5241 3.5H1.47585C0.66076 3.5 0 2.82843 0 2ZM0 11C0 10.1716 0.66076 9.5 1.47585 9.5H23.5241C24.3392 9.5 25 10.1716 25 11C25 11.8284 24.3392 12.5 23.5241 12.5H1.47585C0.66076 12.5 0 11.8284 0 11ZM7.34943 20C7.34943 19.1716 8.01019 18.5 8.82528 18.5L23.5241 18.5C24.3392 18.5 25 19.1716 25 20C25 20.8284 24.3392 21.5 23.5241 21.5L8.82528 21.5C8.01019 21.5 7.34943 20.8284 7.34943 20Z"
      fill={color}
    />
  </svg>
);
export default NavbarIcon;
