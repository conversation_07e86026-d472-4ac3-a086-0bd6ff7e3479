import { IconProps } from "@/types";
import * as React from "react";
const BoxCrossIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 30 30"
    fill="none"
    className={className}
  >
    <path
      d="M9 23.3333L15 17.3333L21 23.3333L23.3333 21L17.3333 15L23.3333 9L21 6.66667L15 12.6667L9 6.66667L6.66667 9L12.6667 15L6.66667 21L9 23.3333ZM3.33333 30C2.41667 30 1.63194 29.6736 0.979167 29.0208C0.326389 28.3681 0 27.5833 0 26.6667V3.33333C0 2.41667 0.326389 1.63194 0.979167 0.979167C1.63194 0.326389 2.41667 0 3.33333 0H26.6667C27.5833 0 28.3681 0.326389 29.0208 0.979167C29.6736 1.63194 30 2.41667 30 3.33333V26.6667C30 27.5833 29.6736 28.3681 29.0208 29.0208C28.3681 29.6736 27.5833 30 26.6667 30H3.33333ZM3.33333 26.6667H26.6667V3.33333H3.33333V26.6667Z"
      fill={color}
    />
  </svg>
);
export default BoxCrossIcon;
