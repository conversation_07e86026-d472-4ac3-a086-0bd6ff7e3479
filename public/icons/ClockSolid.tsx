import { IconProps } from "@/types";
import * as React from "react";
const ClockSolidIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 18 18"
    fill="none"
    className={className}
  >
    <path
      d="M2.96563 3.09766C4.67656 1.38672 6.73906 0.53125 9.15313 0.53125C11.5672 0.53125 13.618 1.38672 15.3055 3.09766C17.0164 4.78516 17.8719 6.83594 17.8719 9.25C17.8719 11.6641 17.0164 13.7266 15.3055 15.4375C13.618 17.125 11.5672 17.9688 9.15313 17.9688C6.73906 17.9688 4.67656 17.125 2.96563 15.4375C1.27813 13.7266 0.434376 11.6641 0.434376 9.25C0.434376 6.83594 1.27813 4.78516 2.96563 3.09766ZM11.157 12.8359C11.3914 13 11.5906 12.9766 11.7547 12.7656L12.7391 11.3945C12.9031 11.1602 12.8797 10.9609 12.6688 10.7969L10.4188 9.17969V4.32812C10.4188 4.04688 10.2781 3.90625 9.99688 3.90625H8.30938C8.02813 3.90625 7.8875 4.04688 7.8875 4.32812V10.2344C7.8875 10.375 7.94609 10.4922 8.06328 10.5859L11.157 12.8359Z"
      fill={color}
    />
  </svg>
);
export default ClockSolidIcon;
