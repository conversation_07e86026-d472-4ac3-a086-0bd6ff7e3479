import { IconProps } from "@/types";
import * as React from "react";
const PhoneSolidIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 22 22"
    fill="none"
    className={className}
  >
    <path
      d="M17.2756 20.7581C17.1059 21.1803 16.8018 21.3528 16.3633 21.2755C11.9316 20.4941 8.42303 18.2571 5.83745 14.5645C3.25188 10.8719 2.3498 6.80979 3.13122 2.37816C3.20855 1.93961 3.47462 1.71284 3.92945 1.69784L7.67667 1.50181C8.13557 1.46373 8.41868 1.68024 8.526 2.15135L9.50413 6.32206C9.60058 6.71985 9.46281 7.02875 9.09084 7.24875L6.71437 8.57895C7.09745 10.0506 7.72829 11.3994 8.60689 12.6253C9.48142 13.8743 10.5331 14.9284 11.7619 15.7876L13.8593 14.0155C14.1702 13.7372 14.5076 13.7134 14.8715 13.9441L18.4561 16.2897C18.839 16.5476 18.9341 16.8857 18.7414 17.3039L17.2756 20.7581Z"
      fill={color}
    />
  </svg>
);
export default PhoneSolidIcon;
