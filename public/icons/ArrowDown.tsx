import { IconProps } from "@/types";
import * as React from "react";
const ArrowDownIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 15 16"
    fill="none"
    className={className}
  >
    <path
      d="M13.7129 6.58618L14.4434 7.34985C14.5983 7.5048 14.6758 7.69295 14.6758 7.91431C14.6758 8.13566 14.5983 8.32381 14.4434 8.47876L8.00195 14.9202C7.84701 15.0751 7.65885 15.1526 7.4375 15.1526C7.21615 15.1526 7.02799 15.0751 6.87305 14.9202L0.431641 8.47876C0.276693 8.32381 0.199219 8.13566 0.199219 7.91431C0.199219 7.69295 0.276693 7.5048 0.431641 7.34985L1.16211 6.58618C1.31706 6.43123 1.50521 6.36483 1.72656 6.38696C1.94792 6.38696 2.13607 6.46444 2.29102 6.61938L6.10938 10.6038V1.07446C6.10938 0.853109 6.18685 0.664958 6.3418 0.51001C6.49674 0.355062 6.6849 0.277588 6.90625 0.277588H7.96875C8.1901 0.277588 8.37826 0.355062 8.5332 0.51001C8.68815 0.664958 8.76562 0.853109 8.76562 1.07446V10.6038L12.584 6.61938C12.7389 6.46444 12.9271 6.38696 13.1484 6.38696C13.3698 6.36483 13.5579 6.43123 13.7129 6.58618Z"
      fill={color}
    />
    <a href="https://www.figma.com/design/JFMavWywuKQ1J2xo3OD92t?node-id=7-413">
      <rect fill="black" fillOpacity={0} width={14.875} height={17} />
    </a>
  </svg>
);
export default ArrowDownIcon;
