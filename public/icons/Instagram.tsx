import { IconProps } from "@/types";
import * as React from "react";
const InstagramIcon = ({ className, color, height, width }: IconProps) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 15 16"
    fill="none"
    className={className}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.0343 4.64017C12.0343 5.04865 11.7001 5.38286 11.2916 5.38286C10.8831 5.38286 10.5489 5.04865 10.5489 4.64017C10.5489 4.2317 10.8831 3.89749 11.2916 3.89749C11.7001 3.89749 12.0343 4.2317 12.0343 4.64017ZM7.5782 10.5816C8.81105 10.5816 9.80625 9.58644 9.80625 8.35359C9.80625 7.12073 8.81105 6.12554 7.5782 6.12554C6.34534 6.12554 5.35015 7.12073 5.35015 8.35359C5.35015 9.58644 6.34534 10.5816 7.5782 10.5816ZM7.5782 12.067C5.52839 12.067 3.86478 10.4034 3.86478 8.35359C3.86478 6.30378 5.52839 4.64017 7.5782 4.64017C9.628 4.64017 11.2916 6.30378 11.2916 8.35359C11.2916 10.4034 9.628 12.067 7.5782 12.067ZM1.63673 5.38286C1.63673 3.74153 2.96614 2.41212 4.60746 2.41212H10.5489C12.1903 2.41212 13.5197 3.74153 13.5197 5.38286V11.3243C13.5197 12.9656 12.1903 14.2951 10.5489 14.2951H4.60746C2.96614 14.2951 1.63673 12.9656 1.63673 11.3243V5.38286ZM10.5489 0.926758H4.60746C2.14918 0.926758 0.151367 2.92457 0.151367 5.38286V11.3243C0.151367 13.7826 2.14918 15.7804 4.60746 15.7804H10.5489C13.0072 15.7804 15.005 13.7826 15.005 11.3243V5.38286C15.005 2.92457 13.0072 0.926758 10.5489 0.926758Z"
      fill={color}
    />
  </svg>
);
export default InstagramIcon;
