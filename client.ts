import axios from "axios";
import { NEXT_API_URL } from "./env";
import { errorHandler } from "./lib/utils";
import { getDefaultStore } from "jotai";
import { tokenAtom } from "./hooks/useAuth";

export function getToken() {
  return getDefaultStore().get(tokenAtom);
}

export const client = axios.create({ baseURL: NEXT_API_URL });

// Request Interceptor to attach token
client.interceptors.request.use(
  (config) => {
    // const token = getToken(); // Retrieve token from storage
    const token = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2ODRjNjRjZTUzY2Q2MTU0ZmVmN2M3ZmMiLCJpYXQiOjE3NTAwNzIzMDB9.iCMYEGVTzg9D9p2LWOjBMiSWrrN1tO-Df2lkZUkxujU`; // Example token, replace with actual token retrieval logic
    if (token) {
      config.headers.Authorization = `Bearer ${token}`; // Inject token in headers
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

export const fetchData = <TData, TVariables>(
  query: string,
  variables?: TVariables,
  options?: Record<string, string>
): (() => Promise<TData>) => {
  return async () => {
    try {
      const headers = {
        "Content-Type": "application/json",
        ...options,
      };

      const response = await client.post(
        "/graphql",
        {
          query,
          variables,
        },
        { headers }
      );

      if (response.data.errors) {
        const { message } = response.data.errors[0] || {};
        throw new Error(message || "Error…");
      }

      return response.data.data;
    } catch (error) {
      console.log(error);
    }
  };
};

/** File Upload */

type UploadFile = {
  file: File;
  parentFolderId?: string;
  fileName?: string;
};

type FileUploadResponse = {
  success: boolean;
  message: string;
  filePath: string;
  fileId: string;
};

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

// Uncomment the following code to enable file upload functionality

// export const uploadFile = async (
//   { file, fileName, parentFolderId }: UploadFile,
//   uploadProgress?: (progress: UploadProgress) => void
// ): Promise<FileUploadResponse> => {
//   const formData = new FormData();
//   formData.append("file", file);
//   formData.append("fileName", fileName || file.name);
//   if (parentFolderId) formData.append("parentFolderId", parentFolderId);
//   const { data } = await client.post<FileUploadResponse>(
//     "/storage/upload",
//     formData,
//     {
//       headers: { "Content-Type": "multipart/form-data" },
//       onUploadProgress(progressEvent) {
//         if (progressEvent.total) {
//           const progress: UploadProgress = {
//             loaded: progressEvent.loaded,
//             total: progressEvent.total,
//             percentage: Math.round(
//               (progressEvent.loaded / progressEvent.total) * 100
//             ),
//           };
//           uploadProgress?.(progress);
//         }
//       },
//     }
//   );

//   return data;
// };
