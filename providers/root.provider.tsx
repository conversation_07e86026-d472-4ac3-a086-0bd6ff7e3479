"use client";

import { errorHand<PERSON> } from "@/lib/utils";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import React from "react";

export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError(err) {
        console.log("err");
        errorHandler(err);
      },
    },
  },
});

export default function RootProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
