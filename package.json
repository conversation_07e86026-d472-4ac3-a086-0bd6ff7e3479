{"name": "seeker-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "codegen": "graphql-codegen --watch", "codegen:build": "graphql-codegen"}, "dependencies": {"@auth/core": "0.37.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.66.0", "axios": "^1.8.3", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "graphql": "^16.8.1", "graphql-request": "^5", "jotai": "^2.12.2", "lodash": "^4.17.21", "next": "15.3.3", "next-auth": "^4.24.11", "next-query-params": "^5.1.0", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-simple-star-rating": "^5.1.7", "sonner": "^1.7.4", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@graphql-codegen/cli": "^5.0.2", "@graphql-codegen/client-preset": "^4.2.6", "@graphql-codegen/typescript-react-query": "^6.1.0", "@parcel/watcher": "^2.4.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.3", "graphql-codegen-typescript-client": "0.18.2", "graphql-codegen-typescript-common": "0.18.2", "postcss": "^8.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}