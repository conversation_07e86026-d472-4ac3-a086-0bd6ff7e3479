"use client";

import React from "react";
import { z } from "zod";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { cn } from "@/lib/utils";
import AppButton from "@/components/common/AppButton";

const schema = z.object({
  fullName: z.string().min(1, "This is required"),
  email: z.string().email("This is not a valid email"),
  mobileNumber: z.string().min(1, "This is required"),
  message: z.string().optional(),
});

type FormData = z.infer<typeof schema>;

export default function ContactForm() {
  const methods = useForm<FormData>({
    defaultValues: {
      fullName: "",
      email: "",
      message: "",
      mobileNumber: "",
    },
    resolver: zodResolver(schema),
  });

  const {
    handleSubmit,
    formState: { errors },
  } = methods;

  const onSubmit = async (data: FormData) => {
    try {
      console.log({ data });
    } catch (error) {
      console.error("Something went wrong:", error);
    }
  };
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormProvider {...methods}>
        <div className="flex flex-col gap-[18px] my-4">
          <div className="flex flex-col gap-1">
            <input
              {...methods.register("fullName")}
              id="fullName"
              type="text"
              placeholder="Name"
              aria-describedby={errors.fullName ? "fullName-error" : undefined}
              className={cn(
                "rounded-[5px] px-4 py-5 bg-white outline-none focus:ring-2 focus:ring-primary text-sm",
                errors.fullName?.message && "ring ring-red-400"
              )}
            />
            {errors.fullName && (
              <span id="fullName-error" className="text-red-500 text-sm">
                {errors.fullName.message}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <input
              {...methods.register("email")}
              id="email"
              type="email"
              placeholder="Email"
              aria-describedby={errors.email ? "email-error" : undefined}
              className={cn(
                "rounded-[5px] px-4 py-5 bg-white outline-none focus:ring-2 focus:ring-primary text-sm",
                errors.email?.message && "ring ring-red-400"
              )}
            />
            {errors.email && (
              <span id="email-error" className="text-red-500 text-sm">
                {errors.email.message}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-1">
            <input
              {...methods.register("mobileNumber", {
                required: "Mobile number is required",
                pattern: {
                  value: /^[0-9]*$/,
                  message: "Only numbers are allowed",
                },
              })}
              id="mobileNumber"
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              placeholder="Phone"
              aria-describedby={
                errors.mobileNumber ? "mobileNumber-error" : undefined
              }
              className={cn(
                "rounded-[5px] px-4 py-5 bg-white outline-none focus:ring-2 focus:ring-primary text-sm",
                errors.mobileNumber?.message && "ring ring-red-400"
              )}
              onInput={(e) => {
                const input = e.target as HTMLInputElement; // Type assertion
                input.value = input.value.replace(/\D/g, ""); // Remove non-numeric characters
              }}
            />
            {errors.mobileNumber && (
              <span id="mobileNumber-error" className="text-red-500 text-sm">
                {errors.mobileNumber.message}
              </span>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <textarea
            {...methods.register("message")}
            id="message"
            placeholder="Message"
            className="rounded-[5px] px-4 py-5 h-24 bg-white outline-none focus:ring-2 focus:ring-primary text-sm"
          />
        </div>

        <AppButton
          variant="light-gradient"
          className="uppercase px-8 py-4 mt-4"
        >
          send message
        </AppButton>
      </FormProvider>
    </form>
  );
}
