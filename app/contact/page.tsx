import COLORS from "@/constants/colors";
import ContactForm from "@/forms/contact.form";
import ICONS from "@/public/icons";
import Link from "next/link";
import React from "react";

export const metadata = {
  title: "Seeker - Contact Us",
  description:
    "Get in touch with <PERSON><PERSON> for any inquiries, support, or feedback. We are here to assist you.",
};

export default function ContactPage() {
  return (
    <>
      <div className="w-full h-[150px] lg:h-[357px] contact-banner-gradient flex items-center justify-center">
        <h1 className="text-3xl lg:text-[40px] text-white font-bold container text-center">
          Contact Us
        </h1>
      </div>

      {/* CONTACT FORM */}
      <section className="container mx-auto px-4 xl:px-[75px] py-8 lg:py-[75px] contact-form-gradient  lg:my-[128px] flex flex-col lg:flex-row lg:items-center lg:justify-center gap-[40px] xl:gap-[116px]">
        {/* INFORMATION */}
        <div className="flex flex-col w-full">
          <h2 className="text-3xl lg:text-[40px] leading-9 lg:leading-11 font-bold text-text mb-5 lg:mb-6">
            Get in Touch
          </h2>
          <p className="text-base lg:text-[17px] leading-8 mb-[37px]">
            Suspendisse ultrice gravida dictum fusce placerat ultricies integer
          </p>
          <div className="flex flex-col gap-7.5 px-4 lg:px-9.5 py-6 lg:py-[60px] bg-white rounded-[50px]">
            <div className="flex flex-row items-start gap-4 lg:gap-5">
              <div className="bg-primary/10 rounded-full min-w-[50px] min-h-[50px] lg:min-w-[54px] lg:min-h-[54px] flex items-center justify-center">
                <ICONS.PinSolidIcon
                  width={13}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                  className="lg:w-5 lg:h-5"
                />
              </div>
              <div className="flex flex-col gap-0.5">
                <span className="text-base leading-7.5 text-textgreen/90">
                  Our Address
                </span>
                <span className="text-xl font-bold leading-8 text-secondary">
                  XXXXX XXXXXXXXXXX XXXXX
                </span>
              </div>
            </div>
            <div className="flex flex-row items-start gap-4 lg:gap-5">
              <div className="bg-primary/10 rounded-full min-w-[50px] min-h-[50px] lg:min-w-[54px] lg:min-h-[54px] flex items-center justify-center">
                <ICONS.ClockSolidIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                  className="lg:w-4.5 lg:h-4.5"
                />
              </div>
              <div className="flex flex-col gap-0.5">
                <span className="text-base leading-7.5 text-textgreen/90">
                  Hours of Operation
                </span>
                <span className="text-xl font-bold leading-8 text-secondary">
                  Mon - Fri: 9.00am to 5.00pm
                </span>
                <span className="text-base leading-7.5 text-textgreen/90">
                  [2nd sat Holiday]
                </span>
              </div>
            </div>
            <div className="flex flex-row items-start gap-4 lg:gap-5">
              <div className="bg-primary/10 rounded-full min-w-[50px] min-h-[50px] lg:min-w-[54px] lg:min-h-[54px] flex items-center justify-center">
                <ICONS.PhoneSolidIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                  className="lg:w-4.5 lg:h-4.5"
                />
              </div>
              <div className="flex flex-col gap-0.5">
                <span className="text-base leading-7.5 text-textgreen/90">
                  Contact
                </span>
                <Link
                  href={"tel:*************"}
                  className="text-xl font-bold leading-8 text-secondary"
                >
                  XXXXXXXXXXX
                </Link>
                <Link
                  href={"mailto:<EMAIL>"}
                  className="text-xl font-bold leading-8 text-secondary"
                >
                  <EMAIL>
                </Link>
              </div>
            </div>
          </div>
          <div className="flex flex-row items-center justify-between mt-4 lg:mt-[40px] px-4">
            <div className="flex flex-row items-center gap-2">
              <ICONS.ArrowDownIcon
                width={15}
                height={17}
                color={COLORS.LIGHT.SECONDARY}
              />
              <div className="text-sm lg:text-base text-secondary leading-8">
                Customer Care
              </div>
            </div>
            <div className="flex flex-row items-center gap-2">
              <Link
                href={"/"}
                className="bg-white rounded-full min-w-[35px] min-h-[35px] lg:min-w-[40px] lg:min-h-[40px] flex items-center justify-center"
              >
                <ICONS.FacebookIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                />
              </Link>
              <Link
                href={"/"}
                className="bg-white rounded-full min-w-[35px] min-h-[35px] lg:min-w-[40px] lg:min-h-[40px] flex items-center justify-center"
              >
                <ICONS.InstagramIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                />
              </Link>
              <Link
                href={"/"}
                className="bg-white rounded-full min-w-[35px] min-h-[35px] lg:min-w-[40px] lg:min-h-[40px] flex items-center justify-center"
              >
                <ICONS.PintrestIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                />
              </Link>
              <Link
                href={"/"}
                className="bg-white rounded-full min-w-[35px] min-h-[35px] lg:min-w-[40px] lg:min-h-[40px] flex items-center justify-center"
              >
                <ICONS.TwitterIcon
                  width={18}
                  height={18}
                  color={COLORS.LIGHT.PRIMARY}
                />
              </Link>
            </div>
          </div>
        </div>
        {/*  FORM */}
        <div className="w-full">
          <ContactForm />
        </div>
      </section>
    </>
  );
}
