import { fetchData } from "@/client";
import BarDetail from "@/elements/bar-detail-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
} from "@/generated/graphql";

export default async function BarDetailPage({
  params,
}: {
  params: Promise<{ bar: string }>;
}) {
  const { bar: slugParam } = await params;

  const { bars: bar } = await fetchData<BarsQuery, BarsQueryVariables>(
    BarsDocument,
    {
      barsInput: { slug: slugParam },
      paginationInput: { limit: 1 },
    }
  )();

  return (
    <div className="bg-backgroundlight sm:bg-transparent">
      <BarDetail.BarDetailSection bar={bar.docs[0]} />
      <BarDetail.CallToDownloadSection />
      {/* <BarDetail.ExploreNearbyBars /> */}
      <BarDetail.FAQSection />
    </div>
  );
}
