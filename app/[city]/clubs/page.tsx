import { fetchData } from "@/client";
import PopularCard from "@/components/cards/popular.card";
import ClubsHomePage from "@/elements/clubs-homepage";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubCategoriesDocument,
  ClubCategoriesQuery,
  ClubCategoriesQueryVariables,
  ClubCategoryByNameDocument,
  ClubCategoryByNameQuery,
  ClubCategoryByNameQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import Link from "next/link";
import React from "react";

export const generateMetadata = async (props: {
  params: Promise<{ city: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam } = await props.params;

  return {
    title: `Seeker – Clubs in ${cityNameParam}`,
    description: `Explore the best clubs in ${cityNameParam} with Seeker. Discover top-rated venues and more.`,
    openGraph: {
      title: `Seeker – clubs in ${cityNameParam}`,
      description: `Explore the best clubs in ${cityNameParam} with Seeker. Discover top-rated venues and more.`,
      url: `https://seeker.com/${cityNameParam}/clubs`,
      images: [
        {
          url: `https://seeker.com/images/clubs-${cityNameParam}.jpg`,
          width: 1200,
          height: 630,
          alt: `Best clubs in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function Clubs({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category } = await searchParams;
  const { city: cityParam } = await params;

  const activeCategory = category ? category : undefined;

  const {
    cities: {
      docs: [city],
    },
  } = await fetchData<CitiesQuery, CitiesQueryVariables>(CitiesDocument, {
    citiesInput: { name: cityParam },
  })();

  const { clubCategories } = await fetchData<
    ClubCategoriesQuery,
    ClubCategoriesQueryVariables
  >(ClubCategoriesDocument)();

  let categoryRes: ClubCategoryByNameQuery | undefined;

  if (category?.length) {
    const res = await fetchData<
      ClubCategoryByNameQuery,
      ClubCategoryByNameQueryVariables
    >(ClubCategoryByNameDocument, { name: category })();
    categoryRes = res;
  }

  const { clubs: clubsWithFilter } = await fetchData<
    ClubsQuery,
    ClubsQueryVariables
  >(ClubsDocument, {
    clubsInput: {
      city: city._id,
      categories: categoryRes?.clubCategory?.id
        ? [categoryRes.clubCategory.id]
        : undefined,
    },
  })();

  return (
    <>
      <ClubsHomePage.HeroSection />
      {category ? (
        <section>
          <div className="flex flex-col items-center justify-center sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-9 container my-[65px] lg:my-[76px]">
            {clubsWithFilter?.docs?.map((club) => (
              <Link href={`bars/${club.slug}`} key={club.id}>
                <PopularCard cardType="club" data={club} />
              </Link>
            ))}
          </div>
          <div className="flex flex-col gap-[50px] lg:gap-[28px] container">
            <div className="font-bold text-2xl leading-5 text-secondary">
              Explore Other Clubs
            </div>
            <div className="flex flex-col w-full sm:flex-row sm:items-center gap-5 mb-[72px]">
              {clubCategories?.docs
                ?.filter((category) => category.name !== activeCategory)
                .map((category) => (
                  <Link
                    key={category.id}
                    href={{ query: { category: category.name } }}
                    className="px-[28px] rounded-none sm:px-[28px] py-[11px]  lg:text-xl font-bold leading-5 bg-primary text-white hover:text-background/80 transition-all duration-100 ease-in-out"
                  >
                    {category.name}
                  </Link>
                ))}
            </div>
          </div>
        </section>
      ) : (
        <>
          <ClubsHomePage.BestClubsSection
            categories={clubCategories?.docs}
            clubs={clubsWithFilter}
            city={city}
          />
          <ClubsHomePage.ClubsSection cityId={city.id} />
          <ClubsHomePage.CallToDownloadSection />
        </>
      )}
      <ClubsHomePage.FAQSection />
    </>
  );
}
