import { fetchData } from "@/client";
import ClubDetail from "@/elements/club-detail-page";
import {
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import React from "react";

export default async function ClubDetailPage({
  params,
}: {
  params: Promise<{ club: string }>;
}) {
  const { club: slugParam } = await params;

  const { clubs: club } = await fetchData<ClubsQuery, ClubsQueryVariables>(
    ClubsDocument,
    {
      clubsInput: { slug: slugParam },
    }
  )();

  return (
    <div className="bg-backgroundlight sm:bg-transparent">
      <ClubDetail.ClubDetailSection club={club.docs[0]} />
      <ClubDetail.CallToDownloadSection />
      {/* <ClubDetail.ExploreNearbyClubs /> */}
      <ClubDetail.FAQSection />
    </div>
  );
}
