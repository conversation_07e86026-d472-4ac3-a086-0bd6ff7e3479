import { atomWithStorage } from 'jotai/utils';

export const tokenAtom = atomWithStorage<string | null>('access_token', null);

export const useAuth = () => {
  // const [token] = useAtom(tokenAtom)
  // const nav = useNavigate()
  // console.log(token)
  // useEffect(() => {
  //   if (token === null) {
  //     nav({ to: '/login' })
  //   } else {
  //     nav({ to: '/' })
  //   }
  // }, [token])
};
