import COLORS from "@/constants/colors";
import ICONS from "@/public/icons";
import Image from "next/image";
import React from "react";
import RatingStars from "../common/RatingStars";
import { cn } from "@/lib/utils";

type Props = {
  rating: number;
  name: string;
  avgCost: string;
  image: string;
  className?: string;
};

export default function GridCard({
  avgCost,
  image,
  name,
  rating,
  className,
  ...rest
}: Props) {
  return (
    <div
      className={cn(
        "w-full h-full flex flex-row items-center bg-white rounded-[18px] overflow-hidden border border-inactive/30 shadow-xl shadow-inactive/50",
        className
      )}
      {...rest}
    >
      <Image
        src={image}
        width={150}
        height={180}
        alt="grid-card"
        className="object-cover rounded-[18px] xl:w-[202px] sm:h-[180px] xl:h-[180px] sm:w-[150px] w-[150px] h-[180px]"
      />
      <div className="w-full h-full flex flex-col justify-center pl-4 gap-1">
        <RatingStars className="border-0 gap-1" ratingValue={rating} />
        <p className="font-montserrat text-sm md:text-base font-semibold text-text max-w-[90%] line-clamp-2">
          {name}
        </p>
        <div className="flex flex-row items-center gap-1">
          <ICONS.BeerJugIcon width={20} height={20} color={COLORS.LIGHT.TEXT} />
          <p className="font-montserrat text-sm md:text-base font-semibold text-text mt-1">
            {avgCost}
          </p>
        </div>
      </div>
    </div>
  );
}
