import COLORS from "@/constants/colors";
import { CityNeighborhoodStatsQuery } from "@/generated/graphql";

import ICONS from "@/public/icons";
import Image from "next/image";
import React from "react";

type CityCardProps = {
  cityNeighborhood: CityNeighborhoodStatsQuery["cityNeighborhoodStats"][number];
  city: string;
};
export default function CityCard({ cityNeighborhood, city }: CityCardProps) {
  return (
    <div className="w-[310px] min-w-[310px] bg-white rounded-[21px] flex flex-col justify-center p-[14px] shadow-lg shadow-neutral-300/20 border border-neutral-200/30">
      <div className="w-full overflow-hidden rounded-[21px]">
        <Image
          src={cityNeighborhood?.coverImage || "/svg/tempLogo.svg"}
          width={279}
          height={173}
          alt="card"
          className="object-cover w-[279px] h-[173px] aspect-square md:object-cover transition-all duration-500"
        />
      </div>
      <div className="flex flex-row items-center gap-2.5 mt-[23px] mb-[26px] pl-4">
        <p className="font-montserrat-alternates font-normal text-xl leading-[29px] text-black truncate max-w-[170px]">
          {cityNeighborhood.name}
        </p>
        <div className="w-[1px] h-[18px] bg-neutral-500 mx-2.5" />
        <p className="font-montserrat-alternates font-normal text-base leading-[29px] text-neutral-500">
          {city}
        </p>
      </div>
      <div className="flex flex-row items-center gap-4 mb-2 pl-4">
        <div className="flex flex-row items-center gap-1.5">
          <div className="w-[39px] h-[38px] bg-transparent rounded-full border border-primary p-1 flex items-center justify-center">
            <ICONS.HappyHourIcon
              width={22}
              height={24}
              color={COLORS.LIGHT.PRIMARY}
            />
          </div>
          <div className="font-montserrat font-semibold italic text-primary text-2xl">
            {cityNeighborhood.totalClubs}+
          </div>
        </div>
        <div className="flex flex-row items-center gap-1.5">
          <div className="w-[39px] h-[38px] bg-transparent rounded-full border border-primary p-1 flex items-center justify-center">
            <ICONS.BeerJugsIcon
              width={25}
              height={23}
              color={COLORS.LIGHT.PRIMARY}
            />
          </div>
          <div className="font-montserrat font-semibold italic text-primary text-2xl">
            {cityNeighborhood.totalBars}+
          </div>
        </div>
      </div>
    </div>
  );
}
