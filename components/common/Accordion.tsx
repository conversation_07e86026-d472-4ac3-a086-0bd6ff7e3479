import { cn } from "@/lib/utils";
import React from "react";

const AccordionTrigger = React.memo(function AccordionTrigger({
  isActive,
  children,
  onClick,
  className,
  ...props
}: {
  isActive: boolean;
  onClick?: (index: number) => void;
  children?: React.ReactNode;
} & React.ComponentPropsWithoutRef<"button">) {
  return (
    <button
      onClick={onClick}
      aria-expanded={isActive}
      className={cn(
        "w-full flex items-center text-text font-semibold text-sm lg:text-base gap-2 lg:gap-4 bg-footerbg/30 px-4 cursor-pointer",
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
});

const AccordionItem = React.memo(function AccordionItem({
  isActive,
  children,
}: {
  isActive: boolean;
  children: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        "transition-all ease-in-out duration-300 overflow-hidden",
        isActive ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
      )}
    >
      {children}
    </div>
  );
});

const Accordion = React.memo(function Accordion({
  children,
  className,
  ...props
}: { children?: React.ReactNode } & React.ComponentPropsWithoutRef<"div">) {
  return (
    <div className={cn("w-full", className)} {...props}>
      {children}
    </div>
  );
});

export { AccordionItem, Accordion, AccordionTrigger };
